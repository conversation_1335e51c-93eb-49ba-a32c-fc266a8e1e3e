{"version": 3, "file": "cy.js", "sourceRoot": "", "sources": ["cy.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,WAAW,EAAC,YAAY,EAAC,WAAW,EAAC,eAAe,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,YAAY,EAAC,QAAQ,EAAC,YAAY,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,YAAY,EAAC,MAAM,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,YAAY,EAAC,MAAM,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,YAAY,EAAC,QAAQ,EAAC,YAAY,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"canol nos\",\"canol dydd\",\"yn y bore\",\"yn y prynhawn\",\"min nos\"],[\"canol nos\",\"canol dydd\",\"y bore\",\"y prynhawn\",\"yr hwyr\"],u],[[\"canol nos\",\"canol dydd\",\"bore\",\"prynhawn\",\"min nos\"],[\"canol nos\",\"canol dydd\",\"bore\",\"prynhawn\",\"yr hwyr\"],[\"canol nos\",\"canol dydd\",\"y bore\",\"y prynhawn\",\"yr hwyr\"]],[\"00:00\",\"12:00\",[\"00:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"24:00\"]]];\n"]}