/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['om-ke'] = ["om-KE",[["WD","WB"],u,u],u,[["D","W","Q","R","K","J","S"],["Dil","<PERSON>ix","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>"],["<PERSON>l<PERSON>a","<PERSON>xa<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>"]],u,[["<PERSON>","<PERSON>","M","A","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>a","Gur","<PERSON>","<PERSON>b","<PERSON>","<PERSON>ax","<PERSON>o","<PERSON>g","<PERSON>l","<PERSON>k","<PERSON>","<PERSON>d"],["<PERSON>ajjii","<PERSON>uraand<PERSON>","<PERSON><PERSON><PERSON>sa","<PERSON>ba","<PERSON>aa<PERSON>a","<PERSON>axabajjii","<PERSON>ooleessa","Hagayya","Fuulbana","Onkololeessa","Sadaasa","Muddee"]],[["A","G","B","E","C","W","A","H","F","O","S","M"],["Ama","Gur","Bit","Elb","Cam","Wax","Ado","Hag","Ful","Onk","Sad","Mud"],["Amajjii","Guraandhala","Bitooteessa","Elba","Caamsa","Waxabajjii","Adooleessa","Hagayya","Fuulbana","Onkololeessa","Sadaasa","Muddee"]],[["KD","CE"],u,["Dheengadda Jeesu","CE"]],0,[6,0],["dd/MM/yy","dd-MMM-y","dd MMMM y","EEEE, MMMM d, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"KES","Ksh","KES",{"ETB":["Br"],"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    