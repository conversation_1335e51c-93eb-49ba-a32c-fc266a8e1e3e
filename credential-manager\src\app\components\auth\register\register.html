<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header>
      <mat-card-title>Create Account</mat-card-title>
      <mat-card-subtitle>Join Credential Manager</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter your email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
            Email is required
          </mat-error>
          <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
            Please enter a valid email
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput [type]="hidePassword() ? 'password' : 'text'"
                 formControlName="password" placeholder="Enter your password">
          <button mat-icon-button matSuffix type="button"
                  (click)="togglePasswordVisibility()"
                  [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword()">
            <mat-icon>{{hidePassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
          <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
            Password must be at least 6 characters
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Confirm Password</mat-label>
          <input matInput [type]="hideConfirmPassword() ? 'password' : 'text'"
                 formControlName="confirmPassword" placeholder="Confirm your password">
          <button mat-icon-button matSuffix type="button"
                  (click)="toggleConfirmPasswordVisibility()"
                  [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hideConfirmPassword()">
            <mat-icon>{{hideConfirmPassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
            Please confirm your password
          </mat-error>
          <mat-error *ngIf="registerForm.hasError('passwordMismatch')">
            Passwords do not match
          </mat-error>
        </mat-form-field>

        <button mat-raised-button color="primary" type="submit"
                class="full-width register-button"
                [disabled]="!registerForm.valid || isLoading()">
          <span *ngIf="!isLoading()">Create Account</span>
          <span *ngIf="isLoading()">Creating Account...</span>
        </button>
      </form>
    </mat-card-content>

    <mat-card-actions align="center">
      <p>Already have an account? <a routerLink="/login">Sign in</a></p>
    </mat-card-actions>
  </mat-card>
</div>
