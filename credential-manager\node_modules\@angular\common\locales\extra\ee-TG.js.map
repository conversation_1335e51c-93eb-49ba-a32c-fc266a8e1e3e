{"version": 3, "file": "ee-TG.js", "sourceRoot": "", "sources": ["ee-TG.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"fɔŋli\",\"ŋdi\",\"ŋdɔ\",\"ɣetrɔ\",\"fiẽ\",\"zã\"],u,u],u,[[\"04:00\",\"05:00\"],[\"05:00\",\"12:00\"],[\"12:00\",\"14:00\"],[\"14:00\",\"18:00\"],[\"18:00\",\"21:00\"],[\"21:00\",\"04:00\"]]];\n"]}