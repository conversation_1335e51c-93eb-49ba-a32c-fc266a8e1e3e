.add-credential-dialog {
  width: 100%;
  max-width: 600px;
}

.credential-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.full-width {
  width: 100%;
}

.form-row {
  display: flex;
  gap: 16px;

  .half-width {
    flex: 1;
  }
}

.password-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.password-fields,
.api-key-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

mat-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

mat-dialog-actions {
  padding: 16px 0;
  gap: 8px;
}

@media (max-width: 600px) {
  .form-row {
    flex-direction: column;

    .half-width {
      width: 100%;
    }
  }

  .add-credential-dialog {
    max-width: 95vw;
  }
}