/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['vun'] = ["vun",[["utuko","kyiukonyi"],u,u],u,[["J","J","J","J","A","I","J"],["J<PERSON>","Jtt","Jnn","Jtn","<PERSON>h","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","Jnn","Jtn","<PERSON>h","<PERSON><PERSON>","<PERSON><PERSON>"]],u,[["J","F","M","A","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","Aprilyi","<PERSON>","<PERSON>yi","July<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Oktoba","Novemba","Desemba"]],u,[["KK","BK"],u,["Kabla ya Kristu","Baada ya Kristu"]],1,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"TZS","TSh","Shilingi ya Tanzania",{"JPY":["JP¥","¥"],"TZS":["TSh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    