import { Injectable, inject } from '@angular/core';
import { Firestore, collection, addDoc, updateDoc, deleteDoc, doc, getDocs, query, where, orderBy, onSnapshot } from '@angular/fire/firestore';
import { Observable, BehaviorSubject } from 'rxjs';
import { CredentialType, PasswordCredential, ApiKeyCredential } from '../models/credential.model';
import { AuthService } from './auth.service';
import { EncryptionService } from './encryption.service';

@Injectable({
  providedIn: 'root'
})
export class CredentialService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);
  private encryptionService = inject(EncryptionService);
  
  private credentialsSubject = new BehaviorSubject<CredentialType[]>([]);
  public credentials$ = this.credentialsSubject.asObservable();

  constructor() {
    this.authService.user$.subscribe(user => {
      if (user) {
        this.loadUserCredentials(user.uid);
      } else {
        this.credentialsSubject.next([]);
      }
    });
  }

  private loadUserCredentials(userId: string): void {
    const credentialsRef = collection(this.firestore, 'credentials');
    const q = query(
      credentialsRef,
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    onSnapshot(q, (snapshot) => {
      const credentials: CredentialType[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        const credential = {
          id: doc.id,
          ...data,
          createdAt: data['createdAt'].toDate(),
          updatedAt: data['updatedAt'].toDate()
        } as CredentialType;
        
        // Decrypt sensitive data
        if (credential.type === 'password') {
          (credential as PasswordCredential).password = this.encryptionService.decrypt(credential.password);
        } else if (credential.type === 'api-key') {
          (credential as ApiKeyCredential).apiKey = this.encryptionService.decrypt(credential.apiKey);
        }
        
        credentials.push(credential);
      });
      this.credentialsSubject.next(credentials);
    });
  }

  async addCredential(credential: Omit<CredentialType, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<void> {
    const user = this.authService.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const now = new Date();
    const credentialData = {
      ...credential,
      userId: user.uid,
      createdAt: now,
      updatedAt: now
    };

    // Encrypt sensitive data
    if (credential.type === 'password') {
      (credentialData as PasswordCredential).password = this.encryptionService.encrypt(credential.password);
    } else if (credential.type === 'api-key') {
      (credentialData as ApiKeyCredential).apiKey = this.encryptionService.encrypt(credential.apiKey);
    }

    const credentialsRef = collection(this.firestore, 'credentials');
    await addDoc(credentialsRef, credentialData);
  }

  async updateCredential(id: string, updates: Partial<CredentialType>): Promise<void> {
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };

    // Encrypt sensitive data if being updated
    if (updates.type === 'password' && 'password' in updates) {
      (updateData as Partial<PasswordCredential>).password = this.encryptionService.encrypt(updates.password);
    } else if (updates.type === 'api-key' && 'apiKey' in updates) {
      (updateData as Partial<ApiKeyCredential>).apiKey = this.encryptionService.encrypt(updates.apiKey);
    }

    const credentialRef = doc(this.firestore, 'credentials', id);
    await updateDoc(credentialRef, updateData);
  }

  async deleteCredential(id: string): Promise<void> {
    const credentialRef = doc(this.firestore, 'credentials', id);
    await deleteDoc(credentialRef);
  }

  getCredentials(): CredentialType[] {
    return this.credentialsSubject.value;
  }

  getCredentialById(id: string): CredentialType | undefined {
    return this.credentialsSubject.value.find(cred => cred.id === id);
  }

  getPasswordCredentials(): PasswordCredential[] {
    return this.credentialsSubject.value.filter(cred => cred.type === 'password') as PasswordCredential[];
  }

  getApiKeyCredentials(): ApiKeyCredential[] {
    return this.credentialsSubject.value.filter(cred => cred.type === 'api-key') as ApiKeyCredential[];
  }

  getApiKeysByProvider(provider: string): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred =>
      cred.provider.toLowerCase() === provider.toLowerCase()
    );
  }

  getValidApiKeys(): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred => cred.isValid === true);
  }

  getExpiredApiKeys(): ApiKeyCredential[] {
    const now = new Date();
    return this.getApiKeyCredentials().filter(cred =>
      cred.expiryDate && cred.expiryDate < now
    );
  }

  getApiKeysByUsage(usage: string): ApiKeyCredential[] {
    return this.getApiKeyCredentials().filter(cred => cred.usage === usage);
  }
}
