/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['om'] = ["om",[["WD","WB"],u,u],u,[["S","M","T","W","T","F","S"],["Dil","Wix","Qib","<PERSON>","<PERSON>m","<PERSON>","<PERSON>"],["<PERSON>l<PERSON>a","<PERSON>xa<PERSON>","<PERSON>bxa<PERSON>","<PERSON>oo<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>"]],u,[["J","F","M","A","M","J","J","A","S","O","<PERSON>","<PERSON>"],["<PERSON>a","<PERSON>ur","<PERSON>","<PERSON>b","<PERSON>","<PERSON>ax","<PERSON>o","<PERSON>g","<PERSON>l","Onk","<PERSON>","<PERSON>d"],["<PERSON>ajjii","<PERSON>uraand<PERSON>","<PERSON><PERSON><PERSON>sa","<PERSON>ba","<PERSON>aa<PERSON>a","<PERSON>axabajjii","<PERSON>ool<PERSON>sa","<PERSON>ga<PERSON>","Fuulbana","Onkololeessa","Sadaasa","Muddee"]],u,[["BCE","CE"],u,["Dheengadda Jeesu","CE"]],0,[6,0],["dd/MM/yy","dd-MMM-y","dd MMMM y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"ETB","Br","Itoophiyaa Birrii",{"ETB":["Br"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    