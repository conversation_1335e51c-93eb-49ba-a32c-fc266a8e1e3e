{"version": 3, "file": "sq.js", "sourceRoot": "", "sources": ["sq.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,YAAY,EAAC,YAAY,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,YAAY,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"e mesnatës\",\"e mesditës\",\"e mëngjesit\",\"e paradites\",\"e pasdites\",\"e mbrëmjes\",\"e natës\"],u,u],[[\"mesnatë\",\"mesditë\",\"mëngjes\",\"paradite\",\"pasdite\",\"mbrëmje\",\"natë\"],u,u],[\"00:00\",\"12:00\",[\"04:00\",\"09:00\"],[\"09:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"24:00\"],[\"00:00\",\"04:00\"]]];\n"]}