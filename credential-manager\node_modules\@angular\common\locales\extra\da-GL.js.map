{"version": 3, "file": "da-GL.js", "sourceRoot": "", "sources": ["da-GL.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAC,aAAa,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,YAAY,EAAC,WAAW,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"midnat\",\"om morgenen\",\"om formiddagen\",\"om eftermiddagen\",\"om aftenen\",\"om natten\"],u,u],[[\"midnat\",\"morgen\",\"formiddag\",\"eftermiddag\",\"aften\",\"nat\"],u,u],[\"00:00\",[\"05:00\",\"10:00\"],[\"10:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"24:00\"],[\"00:00\",\"05:00\"]]];\n"]}