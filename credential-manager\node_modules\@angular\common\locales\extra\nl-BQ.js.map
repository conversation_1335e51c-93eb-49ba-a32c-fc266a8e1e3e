{"version": 3, "file": "nl-BQ.js", "sourceRoot": "", "sources": ["nl-BQ.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,EAAC,WAAW,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,EAAC,SAAS,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"middernacht\",\"’s ochtends\",\"’s middags\",\"’s avonds\",\"’s nachts\"],u,u],[[\"middernacht\",\"ochtend\",\"middag\",\"avond\",\"nacht\"],u,u],[\"00:00\",[\"06:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"24:00\"],[\"00:00\",\"06:00\"]]];\n"]}