<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>Credential Manager</mat-card-title>
      <mat-card-subtitle>Sign in to your account</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter your email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
            Email is required
          </mat-error>
          <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
            Please enter a valid email
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput [type]="hidePassword() ? 'password' : 'text'"
                 formControlName="password" placeholder="Enter your password">
          <button mat-icon-button matSuffix type="button"
                  (click)="togglePasswordVisibility()"
                  [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword()">
            <mat-icon>{{hidePassword() ? 'visibility_off' : 'visibility'}}</mat-icon>
          </button>
          <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
          <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
            Password must be at least 6 characters
          </mat-error>
        </mat-form-field>

        <button mat-raised-button color="primary" type="submit"
                class="full-width login-button"
                [disabled]="!loginForm.valid || isLoading()">
          <span *ngIf="!isLoading()">Sign In</span>
          <span *ngIf="isLoading()">Signing In...</span>
        </button>
      </form>
    </mat-card-content>

    <mat-card-actions align="center">
      <p>Don't have an account? <a routerLink="/register">Sign up</a></p>
      <p><a routerLink="/forgot-password">Forgot Password?</a></p>
    </mat-card-actions>
  </mat-card>
</div>
