/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
if (n === 2)
    return 2;
return 5;
}
    global.ng.common.locales['sat'] = ["sat",[["AM","PM"],u,["ᱥᱮᱛᱟᱜ","ᱧᱤᱫᱟᱹ"]],[["AM","PM"],u,u],[["ᱥ","ᱚ","ᱵ","ᱥ","ᱥ","ᱡ","ᱧ"],["ᱥᱤᱸ","ᱚᱛ","ᱵᱟ","ᱥᱟᱹ","ᱥᱟᱹᱨ","ᱡᱟᱹ","ᱧᱩ"],["ᱥᱤᱸᱜᱮ","ᱚᱛᱮ","ᱵᱟᱞᱮ","ᱥᱟᱹᱜᱩᱱ","ᱥᱟᱹᱨᱫᱤ","ᱡᱟᱹᱨᱩᱢ","ᱧᱩᱦᱩᱢ"],["ᱥᱤᱸ","ᱚᱛ","ᱵᱟ","ᱥᱟᱹ","ᱥᱟᱹᱨ","ᱡᱟᱹ","ᱧᱩ"]],u,[["ᱡ","ᱯ","ᱢ","ᱟ","ᱢ","ᱡ","ᱡ","ᱟ","ᱥ","ᱚ","ᱱ","ᱫ"],["ᱡᱟᱱ","ᱯᱷᱟ","ᱢᱟᱨ","ᱟᱯᱨ","ᱢᱮ","ᱡᱩᱱ","ᱡᱩᱞ","ᱟᱜᱟ","ᱥᱮᱯ","ᱚᱠᱴ","ᱱᱟᱣ","ᱫᱤᱥ"],["ᱡᱟᱱᱣᱟᱨᱤ","ᱯᱷᱟᱨᱣᱟᱨᱤ","ᱢᱟᱨᱪ","ᱟᱯᱨᱮᱞ","ᱢᱮ","ᱡᱩᱱ","ᱡᱩᱞᱟᱭ","ᱟᱜᱟᱥᱛ","ᱥᱮᱯᱴᱮᱢᱵᱟᱨ","ᱚᱠᱴᱚᱵᱟᱨ","ᱱᱟᱣᱟᱢᱵᱟᱨ","ᱫᱤᱥᱟᱢᱵᱟᱨ"]],u,[["ᱥᱮᱨᱢᱟ ᱞᱟᱦᱟ","ᱤᱥᱣᱤ"],u,u],0,[0,0],["d/M/yy","d MMM y","d MMMM y","EEEE, d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"INR","₹","ᱥᱤᱧᱚᱛ ᱨᱮᱱᱟᱜ ᱴᱟᱠᱟ",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    