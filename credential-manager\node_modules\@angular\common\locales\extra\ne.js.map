{"version": 3, "file": "ne.js", "sourceRoot": "", "sources": ["ne.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"मध्यरात\",\"मध्यान्ह\",\"बिहान\",\"अपरान्ह\",\"साँझ\",\"बेलुकी\",\"रात\"],u,u],u,[\"00:00\",\"12:00\",[\"04:00\",\"12:00\"],[\"12:00\",\"16:00\"],[\"16:00\",\"19:00\"],[\"19:00\",\"22:00\"],[\"22:00\",\"04:00\"]]];\n"]}