/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['sg'] = ["sg",[["ND","LK"],u,u],u,[["K","S","T","S","K","P","Y"],["Bk1","Bk2","Bk3","Bk4","Bk5","<PERSON>âp","<PERSON>ây"],["Bikua-ôko","<PERSON><PERSON>kua-û<PERSON>","Bïkua-ptâ","Bïkua-usï<PERSON>","<PERSON><PERSON><PERSON><PERSON>-ok<PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["Bk1","Bk2","Bk3","Bk4","Bk5","Lâp","Lây"]],u,[["N","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","N","N","<PERSON>"],["Nye","<PERSON>l","<PERSON>b<PERSON>","<PERSON>u","<PERSON>êl","<PERSON>ön","<PERSON>","<PERSON>ük","<PERSON>vu","<PERSON>b","Nab","<PERSON>k"],["Nyenye","<PERSON>lund<PERSON>gi","<PERSON>bängü","Ngubùe","Bêläwü","Föndo","Lengua","Kükürü","Mvuka","Ngberere","Nabändüru","Kakauka"]],u,[["KnK","NpK"],u,["Kôzo na Krîstu","Na pekô tî Krîstu"]],1,[6,0],["d/M/y","d MMM, y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00;¤-#,##0.00","#E0"],"XAF","FCFA","farânga CFA (BEAC)",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    