import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, catchError, map, timeout } from 'rxjs';
import { ApiKeyCredential, ApiProvider } from '../models/credential.model';

@Injectable({
  providedIn: 'root'
})
export class ApiValidationService {
  private http = inject(HttpClient);

  private apiProviders: ApiProvider[] = [
    {
      name: 'OpenAI',
      validationEndpoint: 'https://api.openai.com/v1/models',
      validationMethod: 'GET',
      validationHeaders: { 'Authorization': 'Bearer {apiKey}' }
    },
    {
      name: 'Google AI',
      validationEndpoint: 'https://generativelanguage.googleapis.com/v1/models',
      validationMethod: 'GET',
      validationParams: { 'key': '{apiKey}' }
    },
    {
      name: 'Anthropic',
      validationEndpoint: 'https://api.anthropic.com/v1/messages',
      validationMethod: 'POST',
      validationHeaders: { 
        'x-api-key': '{apiKey}',
        'anthropic-version': '2023-06-01',
        'content-type': 'application/json'
      }
    },
    {
      name: 'GitHub',
      validationEndpoint: 'https://api.github.com/user',
      validationMethod: 'GET',
      validationHeaders: { 'Authorization': 'token {apiKey}' }
    },
    {
      name: 'Firebase',
      validationEndpoint: 'https://firebase.googleapis.com/v1beta1/projects',
      validationMethod: 'GET',
      validationParams: { 'key': '{apiKey}' }
    }
  ];

  async validateApiKey(credential: ApiKeyCredential): Promise<{
    isValid: boolean;
    error?: string;
  }> {
    const provider = this.apiProviders.find(p => 
      p.name.toLowerCase() === credential.provider.toLowerCase()
    );

    if (!provider) {
      return {
        isValid: false,
        error: 'Unknown API provider'
      };
    }

    try {
      const result = await this.makeValidationRequest(provider, credential.apiKey).toPromise();
      return {
        isValid: true
      };
    } catch (error: any) {
      return {
        isValid: false,
        error: this.getErrorMessage(error)
      };
    }
  }

  private makeValidationRequest(provider: ApiProvider, apiKey: string): Observable<any> {
    const url = provider.validationEndpoint!;
    let headers = new HttpHeaders();
    let params: any = {};

    // Set up headers
    if (provider.validationHeaders) {
      Object.entries(provider.validationHeaders).forEach(([key, value]) => {
        const headerValue = value.replace('{apiKey}', apiKey);
        headers = headers.set(key, headerValue);
      });
    }

    // Set up query parameters
    if (provider.validationParams) {
      Object.entries(provider.validationParams).forEach(([key, value]) => {
        params[key] = value.replace('{apiKey}', apiKey);
      });
    }

    const options = {
      headers,
      params
    };

    if (provider.validationMethod === 'GET') {
      return this.http.get(url, options).pipe(
        timeout(10000),
        map(() => true),
        catchError(error => {
          // For some APIs, a 401/403 means invalid key, but 200 means valid
          if (error.status === 401 || error.status === 403) {
            throw new Error('Invalid API key');
          }
          // Other errors might still indicate the key is valid but request failed
          if (error.status >= 400 && error.status < 500) {
            throw new Error('Invalid API key');
          }
          throw error;
        })
      );
    } else {
      // For POST requests, send minimal data
      const body = provider.name === 'Anthropic' ? {
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'test' }]
      } : {};

      return this.http.post(url, body, options).pipe(
        timeout(10000),
        map(() => true),
        catchError(error => {
          if (error.status === 401 || error.status === 403) {
            throw new Error('Invalid API key');
          }
          if (error.status >= 400 && error.status < 500) {
            throw new Error('Invalid API key');
          }
          throw error;
        })
      );
    }
  }

  private getErrorMessage(error: any): string {
    if (error.message) {
      return error.message;
    }
    if (error.status === 0) {
      return 'Network error - unable to reach API endpoint';
    }
    if (error.status === 401 || error.status === 403) {
      return 'Invalid API key';
    }
    if (error.status === 429) {
      return 'Rate limit exceeded';
    }
    if (error.status >= 500) {
      return 'Server error';
    }
    return 'Unknown error occurred';
  }

  getApiProviders(): ApiProvider[] {
    return this.apiProviders;
  }

  addCustomProvider(provider: ApiProvider): void {
    this.apiProviders.push(provider);
  }

  async validateMultipleKeys(credentials: ApiKeyCredential[]): Promise<{
    [keyId: string]: { isValid: boolean; error?: string; }
  }> {
    const results: { [keyId: string]: { isValid: boolean; error?: string; } } = {};

    const validationPromises = credentials.map(async (credential) => {
      if (credential.id) {
        const result = await this.validateApiKey(credential);
        results[credential.id] = result;
      }
    });

    await Promise.all(validationPromises);
    return results;
  }

  async validateKeysByProvider(provider: string, credentials: ApiKeyCredential[]): Promise<{
    validKeys: ApiKeyCredential[];
    invalidKeys: ApiKeyCredential[];
  }> {
    const providerKeys = credentials.filter(cred =>
      cred.provider.toLowerCase() === provider.toLowerCase()
    );

    const validKeys: ApiKeyCredential[] = [];
    const invalidKeys: ApiKeyCredential[] = [];

    for (const key of providerKeys) {
      const result = await this.validateApiKey(key);
      if (result.isValid) {
        validKeys.push(key);
      } else {
        invalidKeys.push(key);
      }
    }

    return { validKeys, invalidKeys };
  }
}
