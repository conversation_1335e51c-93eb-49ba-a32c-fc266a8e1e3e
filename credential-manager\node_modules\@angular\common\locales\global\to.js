/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['to'] = ["to",[["AM","PM"],u,["heng<PERSON><PERSON>i","efiafi"]],[["AM","PM"],u,["HH","EA"]],[["S","M","T","P","T","F","T"],["Sāp","Mōn","Tūs","Pul","Tuʻa","Fal","Tok"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON>ni<PERSON>","<PERSON>ū<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON>ʻ<PERSON><PERSON><PERSON>lu","<PERSON>alaite","<PERSON><PERSON><PERSON>"],["<PERSON>āp","<PERSON>ōn","Tūs","<PERSON>ul","Tuʻa","Fal","Tok"]],u,[["S","F","M","E","M","S","S","A","S","O","N","T"],["Sān","Fēp","Maʻa","ʻEpe","Mē","Sun","Siu","ʻAok","Sep","ʻOka","Nōv","Tīs"],["Sānuali","Fēpueli","Maʻasi","ʻEpeleli","Mē","Sune","Siulai","ʻAokosi","Sepitema","ʻOkatopa","Nōvema","Tīsema"]],u,[["KM","TS"],u,["ki muʻa","taʻu ʻo Sīsū"]],1,[6,0],["d/M/yy","d MMM y","d MMMM y","EEEE d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}","{1}, {0}",u,u],[".",",",";","%","+","-","E","×","‰","∞","TF",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"TOP","T$","Paʻanga fakatonga",{"AUD":["AUD$","AU$"],"FJD":[u,"F$"],"JPY":["JP¥","¥"],"NZD":["NZD$","NZ$"],"SBD":[u,"S$"],"TOP":["T$"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    