export interface Credential {
  id?: string;
  userId: string;
  title: string;
  type: 'password' | 'api-key';
  createdAt: Date;
  updatedAt: Date;
}

export interface PasswordCredential extends Credential {
  type: 'password';
  username?: string;
  email?: string;
  password: string;
  website?: string;
  notes?: string;
}

export interface ApiKeyCredential extends Credential {
  type: 'api-key';
  provider: string;
  apiKey: string;
  keyName?: string; // Custom name for this specific key (e.g., "Production Key", "Development Key")
  description?: string;
  isValid?: boolean;
  lastValidated?: Date;
  validationError?: string;
  endpoint?: string;
  usage?: 'production' | 'development' | 'testing' | 'personal' | 'other';
  expiryDate?: Date;
  monthlyLimit?: number;
  currentUsage?: number;
}

export type CredentialType = PasswordCredential | ApiKeyCredential;

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  createdAt: Date;
}

export interface ApiProvider {
  name: string;
  validationEndpoint?: string;
  validationMethod?: 'GET' | 'POST';
  validationHeaders?: { [key: string]: string };
  validationParams?: { [key: string]: string };
}
